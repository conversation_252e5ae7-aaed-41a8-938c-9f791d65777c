# Details

Date : 2025-07-16 21:05:55

Directory d:\\MyProjects\\Selfdiscipline\\Combination

Total : 60 files,  67887 codes, 1545 comments, 3158 blanks, all 72590 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [DEPLOYMENT\_GUIDE.md](/DEPLOYMENT_GUIDE.md) | Markdown | 197 | 0 | 45 | 242 |
| [FUSION\_SUMMARY.md](/FUSION_SUMMARY.md) | Markdown | 138 | 0 | 36 | 174 |
| [Interop.UIAutomationClient.10.19041.0/build/Interop.UIAutomationClient.targets](/Interop.UIAutomationClient.10.19041.0/build/Interop.UIAutomationClient.targets) | XML | 13 | 0 | 1 | 14 |
| [Interop.UIAutomationClient.10.19041.0/tools/install.ps1](/Interop.UIAutomationClient.10.19041.0/tools/install.ps1) | PowerShell | 3 | 0 | 1 | 4 |
| [System.CodeDom.8.0.0/buildTransitive/net461/System.CodeDom.targets](/System.CodeDom.8.0.0/buildTransitive/net461/System.CodeDom.targets) | XML | 6 | 0 | 1 | 7 |
| [System.CodeDom.8.0.0/buildTransitive/netcoreapp2.0/System.CodeDom.targets](/System.CodeDom.8.0.0/buildTransitive/netcoreapp2.0/System.CodeDom.targets) | XML | 6 | 0 | 1 | 7 |
| [System.CodeDom.8.0.0/lib/net462/System.CodeDom.xml](/System.CodeDom.8.0.0/lib/net462/System.CodeDom.xml) | XML | 4,402 | 0 | 52 | 4,454 |
| [System.CodeDom.8.0.0/lib/net6.0/System.CodeDom.xml](/System.CodeDom.8.0.0/lib/net6.0/System.CodeDom.xml) | XML | 4,402 | 0 | 52 | 4,454 |
| [System.CodeDom.8.0.0/lib/net7.0/System.CodeDom.xml](/System.CodeDom.8.0.0/lib/net7.0/System.CodeDom.xml) | XML | 4,402 | 0 | 52 | 4,454 |
| [System.CodeDom.8.0.0/lib/net8.0/System.CodeDom.xml](/System.CodeDom.8.0.0/lib/net8.0/System.CodeDom.xml) | XML | 4,402 | 0 | 52 | 4,454 |
| [System.CodeDom.8.0.0/lib/netstandard2.0/System.CodeDom.xml](/System.CodeDom.8.0.0/lib/netstandard2.0/System.CodeDom.xml) | XML | 4,402 | 0 | 52 | 4,454 |
| [System.Management.8.0.0/PACKAGE.md](/System.Management.8.0.0/PACKAGE.md) | Markdown | 41 | 6 | 24 | 71 |
| [System.Management.8.0.0/buildTransitive/netcoreapp2.0/System.Management.targets](/System.Management.8.0.0/buildTransitive/netcoreapp2.0/System.Management.targets) | XML | 6 | 0 | 1 | 7 |
| [System.Management.8.0.0/lib/net6.0/System.Management.xml](/System.Management.8.0.0/lib/net6.0/System.Management.xml) | XML | 2,529 | 0 | 0 | 2,529 |
| [System.Management.8.0.0/lib/net7.0/System.Management.xml](/System.Management.8.0.0/lib/net7.0/System.Management.xml) | XML | 2,529 | 0 | 0 | 2,529 |
| [System.Management.8.0.0/lib/net8.0/System.Management.xml](/System.Management.8.0.0/lib/net8.0/System.Management.xml) | XML | 2,529 | 0 | 0 | 2,529 |
| [System.Management.8.0.0/lib/netstandard2.0/System.Management.xml](/System.Management.8.0.0/lib/netstandard2.0/System.Management.xml) | XML | 2,529 | 0 | 0 | 2,529 |
| [System.Management.8.0.0/runtimes/win/lib/net6.0/System.Management.xml](/System.Management.8.0.0/runtimes/win/lib/net6.0/System.Management.xml) | XML | 9,401 | 0 | 371 | 9,772 |
| [System.Management.8.0.0/runtimes/win/lib/net7.0/System.Management.xml](/System.Management.8.0.0/runtimes/win/lib/net7.0/System.Management.xml) | XML | 9,330 | 0 | 371 | 9,701 |
| [System.Management.8.0.0/runtimes/win/lib/net8.0/System.Management.xml](/System.Management.8.0.0/runtimes/win/lib/net8.0/System.Management.xml) | XML | 9,330 | 0 | 371 | 9,701 |
| [UIAComWrapper.********/tools/install.ps1](/UIAComWrapper.********/tools/install.ps1) | PowerShell | 10 | 0 | 3 | 13 |
| [backend/guardian\_service.py](/backend/guardian_service.py) | Python | 342 | 98 | 98 | 538 |
| [backend/unified\_guardian\_service.py](/backend/unified_guardian_service.py) | Python | 587 | 137 | 163 | 887 |
| [build\_script.py](/build_script.py) | Python | 230 | 39 | 60 | 329 |
| [common/\_\_init\_\_.py](/common/__init__.py) | Python | 44 | 14 | 10 | 68 |
| [common/ai\_manager.py](/common/ai_manager.py) | Python | 188 | 56 | 56 | 300 |
| [common/config\_handler.py](/common/config_handler.py) | Python | 303 | 114 | 68 | 485 |
| [common/cryption.py](/common/cryption.py) | Python | 48 | 57 | 19 | 124 |
| [common/database\_manager.py](/common/database_manager.py) | Python | 254 | 34 | 69 | 357 |
| [common/notification\_manager.py](/common/notification_manager.py) | Python | 168 | 75 | 51 | 294 |
| [common/process\_utils.py](/common/process_utils.py) | Python | 136 | 76 | 37 | 249 |
| [common/system\_utils.py](/common/system_utils.py) | Python | 315 | 203 | 68 | 586 |
| [common/time\_utils.py](/common/time_utils.py) | Python | 113 | 71 | 32 | 216 |
| [frontend/launcher.py](/frontend/launcher.py) | Python | 67 | 25 | 25 | 117 |
| [frontend/static/css/style.css](/frontend/static/css/style.css) | PostCSS | 267 | 18 | 64 | 349 |
| [frontend/static/js/app.js](/frontend/static/js/app.js) | JavaScript | 429 | 31 | 58 | 518 |
| [frontend/templates/index.html](/frontend/templates/index.html) | HTML | 268 | 11 | 12 | 291 |
| [frontend/web\_control\_panel.py](/frontend/web_control_panel.py) | Python | 635 | 68 | 103 | 806 |
| [installer/setup.py](/installer/setup.py) | Python | 310 | 76 | 93 | 479 |
| [intelligent\_monitor/README.md](/intelligent_monitor/README.md) | Markdown | 101 | 0 | 29 | 130 |
| [intelligent\_monitor/REFACTORING\_SUMMARY.md](/intelligent_monitor/REFACTORING_SUMMARY.md) | Markdown | 115 | 0 | 32 | 147 |
| [intelligent\_monitor/\_\_init\_\_.py](/intelligent_monitor/__init__.py) | Python | 0 | 1 | 1 | 2 |
| [intelligent\_monitor/config.py](/intelligent_monitor/config.py) | Python | 47 | 33 | 7 | 87 |
| [intelligent\_monitor/core/\_\_init\_\_.py](/intelligent_monitor/core/__init__.py) | Python | 0 | 1 | 1 | 2 |
| [intelligent\_monitor/core/uia\_base.py](/intelligent_monitor/core/uia_base.py) | Python | 75 | 15 | 11 | 101 |
| [intelligent\_monitor/gui.py](/intelligent_monitor/gui.py) | Python | 283 | 23 | 50 | 356 |
| [intelligent\_monitor/introduce.md](/intelligent_monitor/introduce.md) | Markdown | 215 | 0 | 45 | 260 |
| [intelligent\_monitor/main.py](/intelligent_monitor/main.py) | Python | 156 | 27 | 36 | 219 |
| [intelligent\_monitor/modules/\_\_init\_\_.py](/intelligent_monitor/modules/__init__.py) | Python | 0 | 1 | 1 | 2 |
| [intelligent\_monitor/modules/blacklist\_checker.py](/intelligent_monitor/modules/blacklist_checker.py) | Python | 23 | 6 | 5 | 34 |
| [intelligent\_monitor/modules/browser\_info\_fetcher.py](/intelligent_monitor/modules/browser_info_fetcher.py) | Python | 130 | 23 | 30 | 183 |
| [intelligent\_monitor/modules/managers.py](/intelligent_monitor/modules/managers.py) | Python | 279 | 68 | 33 | 380 |
| [intelligent\_monitor/modules/tab\_closer.py](/intelligent_monitor/modules/tab_closer.py) | Python | 179 | 39 | 45 | 263 |
| [intelligent\_monitor/test\_modules.py](/intelligent_monitor/test_modules.py) | Python | 118 | 18 | 26 | 162 |
| [proxy/requesttest.py](/proxy/requesttest.py) | Python | 216 | 30 | 45 | 291 |
| [proxy/user\_agent\_cache.json](/proxy/user_agent_cache.json) | JSON | 3 | 0 | 0 | 3 |
| [test\_fusion.py](/test_fusion.py) | Python | 165 | 34 | 46 | 245 |
| [uninstall.py](/uninstall.py) | Python | 97 | 17 | 20 | 134 |
| [融合蓝图.md](/%E8%9E%8D%E5%90%88%E8%93%9D%E5%9B%BE.md) | Markdown | 178 | 0 | 40 | 218 |
| [项目蓝图.md](/%E9%A1%B9%E7%9B%AE%E8%93%9D%E5%9B%BE.md) | Markdown | 196 | 0 | 83 | 279 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)