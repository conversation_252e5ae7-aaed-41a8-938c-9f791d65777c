# 自律守护者 v7.0 融合版 - 部署指南

## 项目概述

自律守护者 v7.0 融合版是将原有的"自律守护者 V6.0"和"智能浏览器监控系统 v8.0"两个项目融合而成的统一解决方案。它提供了以下核心功能：

### 核心功能
1. **统一后台守护** - 单一Windows服务管理所有功能模块
2. **双层过滤系统** - 全局进程扫描 + 焦点内容监控
3. **AI驱动的内容审核** - 支持OpenAI和Gemini的文本审核
4. **智能视觉分析** - 自动截图、AI分类、时间管理
5. **拒绝后交互** - 临时豁免机制和用户通知

## 系统架构

```
自律守护者 v7.0 融合版
├── backend/
│   └── unified_guardian_service.py  # 统一守护服务
├── frontend/
│   ├── web_control_panel.py         # Web控制面板
│   └── launcher.py                  # 安全启动器
├── common/
│   ├── config_handler.py            # 配置管理器
│   ├── database_manager.py          # 数据库管理器
│   ├── ai_manager.py                # AI管理器
│   ├── notification_manager.py      # 通知管理器
│   └── system_utils.py              # 系统工具
└── intelligent_monitor/             # 智能监控模块
    ├── core/
    ├── modules/
    └── config.py
```

## 环境要求

### 系统要求
- Windows 10/11 (64位)
- 管理员权限
- 网络连接（用于时间同步和AI API调用）

### Python依赖
```
cryptography>=3.4.8
psutil>=5.8.0
pywin32>=301
ntplib>=0.3.4
flask>=2.0.0
requests>=2.25.0
Pillow>=8.0.0
UIAComWrapper>=1.1.0
pythonnet>=3.0.0
```

## 构建步骤

### 1. 环境准备
```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装PyInstaller
pip install pyinstaller
```

### 2. 运行构建脚本
```bash
python build_script.py
```

构建脚本将：
- 清理旧的构建文件
- 构建所有组件为exe文件
- 整理输出到 `Project-SelfDiscipline-v7/` 目录

### 3. 构建输出
```
Project-SelfDiscipline-v7/
├── backend/
│   └── unified_guardian_service.exe
├── frontend/
│   ├── control_panel.exe
│   └── launcher.exe
├── installer/
│   └── setup.exe
└── README.txt
```

## 配置说明

### 配置文件结构
融合版使用分层配置结构：

```json
{
  "version": "7.0",
  "installation_path": "C:\\Program Files\\Guardian",
  "service_name": "WinSecEnhSvc",
  "features": {
    "enable_global_process_scan": true,
    "enable_focus_content_monitor": true,
    "enable_vision_analysis": true
  },
  "rules": {
    "process_blacklist": ["game.exe", "game2.exe,20:00,22:00"],
    "browser_content_blacklist": {
      "keywords": ["游戏", "广告"],
      "urls": ["example.com"]
    },
    "time_limit_rules": {
      "游戏": {"limit_minutes_per_day": 60, "enabled": true}
    }
  },
  "ai_config": {
    "text_audit": {
      "enabled": true,
      "provider": "openai",
      "api_key": "",
      "model": "gpt-3.5-turbo"
    },
    "vision_audit": {
      "enabled": true,
      "focus_time_trigger_minutes": 30,
      "provider": "openai",
      "model": "gpt-4-vision-preview"
    }
  }
}
```

### AI配置
1. **文本审核**：用于浏览器内容过滤
2. **视觉分析**：用于应用自动分类

支持的AI提供商：
- OpenAI (推荐)
- Gemini (待实现)

## 部署流程

### 1. 开发环境测试
```bash
# 运行功能测试
python test_fusion.py

# 启动Web控制面板（开发模式）
python frontend/web_control_panel.py

# 测试统一守护服务（开发模式）
python backend/unified_guardian_service.py
```

### 2. 生产环境部署
1. 构建所有组件
2. 以管理员权限运行安装程序
3. 配置AI API密钥
4. 设置规则和时间限制

### 3. 服务管理
```bash
# 安装服务
unified_guardian_service.exe install

# 启动服务
sc start WinSecEnhSvc

# 停止服务
sc stop WinSecEnhSvc

# 删除服务
unified_guardian_service.exe remove
```

## 功能验证

### 1. 基本功能测试
- [ ] 配置文件读写正常
- [ ] 数据库操作正常
- [ ] AI管理器响应正常
- [ ] 通知系统工作正常
- [ ] Web控制面板可访问

### 2. 核心功能测试
- [ ] 全局进程扫描生效
- [ ] 浏览器内容监控生效
- [ ] AI文本审核工作
- [ ] 视觉分析和分类工作
- [ ] 时间限制规则生效
- [ ] 临时豁免机制工作

### 3. 集成测试
- [ ] 多功能模块协同工作
- [ ] 通知系统正确显示
- [ ] 配置更改实时生效
- [ ] 服务自恢复机制工作

## 故障排除

### 常见问题
1. **权限不足**：确保以管理员权限运行
2. **依赖缺失**：检查Python依赖是否完整安装
3. **AI API失败**：检查API密钥和网络连接
4. **UIA组件失败**：确保UIAComWrapper正确安装

### 日志查看
- Windows事件日志：查看服务运行状态
- 控制台输出：开发模式下的详细日志
- 配置文件：检查配置是否正确加载

## 安全注意事项

1. **API密钥安全**：妥善保管AI服务的API密钥
2. **权限控制**：服务运行需要适当的系统权限
3. **数据隐私**：截图和内容数据的处理需符合隐私要求
4. **网络安全**：AI API调用需要安全的网络连接

## 后续开发

### 待完成功能
1. Gemini AI支持的完整实现
2. 更多浏览器的支持
3. 高级时间管理规则
4. 用户行为分析和报告

### 扩展方向
1. 移动端支持
2. 云端同步
3. 多用户管理
4. 企业级部署

## 技术支持

如遇到问题，请检查：
1. 系统要求是否满足
2. 依赖是否正确安装
3. 配置是否正确设置
4. 日志中的错误信息

---

**注意**：本项目为融合版本，整合了多个复杂系统。部署前请仔细阅读本指南并进行充分测试。
