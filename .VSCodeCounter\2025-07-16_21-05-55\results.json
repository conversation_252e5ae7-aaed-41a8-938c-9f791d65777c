{"file:///d%3A/MyProjects/Selfdiscipline/Combination/uninstall.py": {"language": "Python", "code": 97, "comment": 17, "blank": 20}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/DEPLOYMENT_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 197, "comment": 0, "blank": 45}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/%E8%9E%8D%E5%90%88%E8%93%9D%E5%9B%BE.md": {"language": "<PERSON><PERSON>", "code": 178, "comment": 0, "blank": 40}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/test_fusion.py": {"language": "Python", "code": 165, "comment": 34, "blank": 46}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/__init__.py": {"language": "Python", "code": 0, "comment": 1, "blank": 1}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/test_modules.py": {"language": "Python", "code": 118, "comment": 18, "blank": 26}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/modules/__init__.py": {"language": "Python", "code": 0, "comment": 1, "blank": 1}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/modules/tab_closer.py": {"language": "Python", "code": 179, "comment": 39, "blank": 45}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/modules/managers.py": {"language": "Python", "code": 279, "comment": 68, "blank": 33}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/modules/browser_info_fetcher.py": {"language": "Python", "code": 130, "comment": 23, "blank": 30}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/modules/blacklist_checker.py": {"language": "Python", "code": 23, "comment": 6, "blank": 5}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/main.py": {"language": "Python", "code": 156, "comment": 27, "blank": 36}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/README.md": {"language": "<PERSON><PERSON>", "code": 101, "comment": 0, "blank": 29}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/introduce.md": {"language": "<PERSON><PERSON>", "code": 215, "comment": 0, "blank": 45}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.Management.8.0.0/PACKAGE.md": {"language": "<PERSON><PERSON>", "code": 41, "comment": 6, "blank": 24}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/gui.py": {"language": "Python", "code": 283, "comment": 23, "blank": 50}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/%E9%A1%B9%E7%9B%AE%E8%93%9D%E5%9B%BE.md": {"language": "<PERSON><PERSON>", "code": 196, "comment": 0, "blank": 83}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/UIAComWrapper.********/tools/install.ps1": {"language": "PowerShell", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/REFACTORING_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 115, "comment": 0, "blank": 32}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/core/uia_base.py": {"language": "Python", "code": 75, "comment": 15, "blank": 11}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.Management.8.0.0/lib/net7.0/System.Management.xml": {"language": "XML", "code": 2529, "comment": 0, "blank": 0}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/config.py": {"language": "Python", "code": 47, "comment": 33, "blank": 7}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.Management.8.0.0/lib/net8.0/System.Management.xml": {"language": "XML", "code": 2529, "comment": 0, "blank": 0}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/intelligent_monitor/core/__init__.py": {"language": "Python", "code": 0, "comment": 1, "blank": 1}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.Management.8.0.0/lib/net6.0/System.Management.xml": {"language": "XML", "code": 2529, "comment": 0, "blank": 0}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.Management.8.0.0/lib/netstandard2.0/System.Management.xml": {"language": "XML", "code": 2529, "comment": 0, "blank": 0}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.Management.8.0.0/runtimes/win/lib/net7.0/System.Management.xml": {"language": "XML", "code": 9330, "comment": 0, "blank": 371}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.Management.8.0.0/buildTransitive/netcoreapp2.0/System.Management.targets": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.Management.8.0.0/runtimes/win/lib/net8.0/System.Management.xml": {"language": "XML", "code": 9330, "comment": 0, "blank": 371}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.CodeDom.8.0.0/lib/net8.0/System.CodeDom.xml": {"language": "XML", "code": 4402, "comment": 0, "blank": 52}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.CodeDom.8.0.0/lib/netstandard2.0/System.CodeDom.xml": {"language": "XML", "code": 4402, "comment": 0, "blank": 52}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.CodeDom.8.0.0/buildTransitive/net461/System.CodeDom.targets": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/proxy/requesttest.py": {"language": "Python", "code": 216, "comment": 30, "blank": 45}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/proxy/user_agent_cache.json": {"language": "JSON", "code": 3, "comment": 0, "blank": 0}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.CodeDom.8.0.0/lib/net7.0/System.CodeDom.xml": {"language": "XML", "code": 4402, "comment": 0, "blank": 52}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/installer/setup.py": {"language": "Python", "code": 310, "comment": 76, "blank": 93}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/FUSION_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 138, "comment": 0, "blank": 36}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/frontend/launcher.py": {"language": "Python", "code": 67, "comment": 25, "blank": 25}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.CodeDom.8.0.0/buildTransitive/netcoreapp2.0/System.CodeDom.targets": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/Interop.UIAutomationClient.10.19041.0/build/Interop.UIAutomationClient.targets": {"language": "XML", "code": 13, "comment": 0, "blank": 1}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/common/notification_manager.py": {"language": "Python", "code": 168, "comment": 75, "blank": 51}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/build_script.py": {"language": "Python", "code": 230, "comment": 39, "blank": 60}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/backend/guardian_service.py": {"language": "Python", "code": 342, "comment": 98, "blank": 98}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/common/process_utils.py": {"language": "Python", "code": 136, "comment": 76, "blank": 37}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/common/database_manager.py": {"language": "Python", "code": 254, "comment": 34, "blank": 69}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/common/__init__.py": {"language": "Python", "code": 44, "comment": 14, "blank": 10}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/backend/unified_guardian_service.py": {"language": "Python", "code": 587, "comment": 137, "blank": 163}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/common/time_utils.py": {"language": "Python", "code": 113, "comment": 71, "blank": 32}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/common/system_utils.py": {"language": "Python", "code": 315, "comment": 203, "blank": 68}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/frontend/web_control_panel.py": {"language": "Python", "code": 635, "comment": 68, "blank": 103}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/Interop.UIAutomationClient.10.19041.0/tools/install.ps1": {"language": "PowerShell", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.CodeDom.8.0.0/lib/net462/System.CodeDom.xml": {"language": "XML", "code": 4402, "comment": 0, "blank": 52}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/common/ai_manager.py": {"language": "Python", "code": 188, "comment": 56, "blank": 56}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/common/config_handler.py": {"language": "Python", "code": 303, "comment": 114, "blank": 68}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.CodeDom.8.0.0/lib/net6.0/System.CodeDom.xml": {"language": "XML", "code": 4402, "comment": 0, "blank": 52}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/frontend/static/css/style.css": {"language": "PostCSS", "code": 267, "comment": 18, "blank": 64}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/frontend/static/js/app.js": {"language": "JavaScript", "code": 429, "comment": 31, "blank": 58}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/common/cryption.py": {"language": "Python", "code": 48, "comment": 57, "blank": 19}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/frontend/templates/index.html": {"language": "HTML", "code": 268, "comment": 11, "blank": 12}, "file:///d%3A/MyProjects/Selfdiscipline/Combination/System.Management.8.0.0/runtimes/win/lib/net6.0/System.Management.xml": {"language": "XML", "code": 9401, "comment": 0, "blank": 371}}