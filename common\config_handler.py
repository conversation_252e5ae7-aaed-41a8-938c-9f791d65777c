"""
配置处理器 - 作为读写加密配置文件的唯一入口
确保操作的原子性和安全性
黑名单规则现在完全存储在加密配置文件中，不再使用独立的blacklist.txt文件
"""

import os
import tempfile
import shutil
from typing import Dict, Any, List
from . import cryption

# 配置文件路径 - 在安装时会被修改为实际的隐藏路径
# 开发环境使用用户目录，生产环境使用系统目录
if os.path.exists(os.path.join(os.path.dirname(__file__), '..', 'test_fusion.py')) or \
   os.path.exists(os.path.join(os.path.dirname(__file__), '..', 'build_script.py')):
    # 开发环境
    CONFIG_DIR = os.path.join(os.path.expanduser('~'), '.guardian_fusion')
    os.makedirs(CONFIG_DIR, exist_ok=True)
    CONFIG_FILE_PATH = os.path.join(CONFIG_DIR, 'config.dat')
    # 保留旧的blacklist.txt路径用于迁移
    LEGACY_BLACKLIST_FILE_PATH = os.path.join(CONFIG_DIR, 'blacklist.txt')
else:
    # 生产环境
    CONFIG_FILE_PATH = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'SysWOW64', 'config.dat')
    # 保留旧的blacklist.txt路径用于迁移
    LEGACY_BLACKLIST_FILE_PATH = os.path.join(os.path.dirname(CONFIG_FILE_PATH), 'blacklist.txt')

class ConfigHandler:
    def __init__(self):
        self.config_path = CONFIG_FILE_PATH
        self.legacy_blacklist_path = LEGACY_BLACKLIST_FILE_PATH

    def _migrate_legacy_blacklist(self) -> List[str]:
        """
        迁移旧的blacklist.txt文件到配置中

        Returns:
            List[str]: 迁移的黑名单规则列表
        """
        rules = []
        try:
            if os.path.exists(self.legacy_blacklist_path):
                print(f"发现旧的黑名单文件，正在迁移: {self.legacy_blacklist_path}")
                with open(self.legacy_blacklist_path, 'r', encoding='utf-8') as f:
                    for line in f.readlines():
                        line = line.strip()
                        if line and not line.startswith('#'):
                            rules.append(line)

                # 迁移完成后删除旧文件
                try:
                    os.remove(self.legacy_blacklist_path)
                    print(f"已删除旧的黑名单文件: {self.legacy_blacklist_path}")
                except Exception as e:
                    print(f"删除旧黑名单文件失败: {str(e)}")

        except Exception as e:
            print(f"迁移黑名单文件失败: {str(e)}")

        return rules
    
    def load_config(self) -> Dict[str, Any]:
        """
        从加密配置文件加载配置

        Returns:
            dict: 配置字典
        """
        try:
            if not os.path.exists(self.config_path):
                # 如果配置文件不存在，检查是否需要迁移旧的黑名单文件
                config = self._get_default_config()
                legacy_rules = self._migrate_legacy_blacklist()
                if legacy_rules:
                    config['blacklist_rules'] = legacy_rules
                    # 保存迁移后的配置
                    self.save_config(config)
                return config

            with open(self.config_path, 'rb') as f:
                encrypted_data = f.read()

            if not encrypted_data:
                config = self._get_default_config()
                legacy_rules = self._migrate_legacy_blacklist()
                if legacy_rules:
                    config['blacklist_rules'] = legacy_rules
                    self.save_config(config)
                return config

            config_data = cryption.decrypt(encrypted_data)

            # 检查是否需要迁移黑名单规则
            if 'blacklist_rules' not in config_data:
                legacy_rules = self._migrate_legacy_blacklist()
                config_data['blacklist_rules'] = legacy_rules
                # 保存更新后的配置
                self.save_config(config_data)

            return config_data

        except Exception as e:
            # 如果解密失败，返回默认配置
            print(f"加载配置失败，使用默认配置: {str(e)}")
            config = self._get_default_config()
            # 尝试迁移旧的黑名单文件
            legacy_rules = self._migrate_legacy_blacklist()
            if legacy_rules:
                config['blacklist_rules'] = legacy_rules
            return config
    
    def save_config(self, config_data: Dict[str, Any]) -> None:
        """
        保存配置到加密文件

        Args:
            config_data: 要保存的配置字典
        """
        try:
            # 确保黑名单规则字段存在
            if 'blacklist_rules' not in config_data:
                config_data['blacklist_rules'] = []

            # 加密配置数据
            encrypted_data = cryption.encrypt(config_data)

            # 原子性写入：先写入临时文件，再重命名
            temp_path = self.config_path + '.tmp'
            with open(temp_path, 'wb') as f:
                f.write(encrypted_data)

            # 原子性重命名
            shutil.move(temp_path, self.config_path)

        except Exception as e:
            # 清理临时文件
            temp_path = self.config_path + '.tmp'
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            raise Exception(f"保存配置失败: {str(e)}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置 - 融合版本 v7.0

        Returns:
            dict: 默认配置字典
        """
        return {
            'version': '7.0',  # 融合版本
            'installation_path': 'C:\\Program Files\\Guardian',
            'service_name': 'WinSecEnhSvc',
            'features': {
                'enable_global_process_scan': True,
                'enable_focus_content_monitor': True,
                'enable_vision_analysis': True
            },
            'rules': {
                'process_blacklist': [
                    # 示例规则：
                    # "game1.exe",
                    # "game2.exe,20:00,22:00"
                ],
                'browser_content_blacklist': {
                    'keywords': ['游戏', '广告', '抽奖'],
                    'urls': ['example-malicious-domain.com']
                },
                'allowed_browsers': [
                    'chrome', 'msedge', 'firefox', 'opera', 'brave', 'vivaldi'
                ],
                'time_limit_rules': {
                    '游戏': {'limit_minutes_per_day': 60, 'enabled': True},
                    '聊天软件': {'limit_minutes_per_day': 120, 'enabled': False}
                }
            },
            'ai_config': {
                'text_audit': {
                    'enabled': True,
                    'provider': 'openai',  # 'openai' or 'gemini'
                    'api_key': '',
                    'api_endpoint': 'https://openrouter.ai/api/v1/chat/completions',
                    'model': 'google/gemma-3-27b-it:free',
                    'prompt_template': '作为一名网络内容审核员，请判断以下网页内容是否合规。标题：{title}，URL：{url}。如果内容涉及游戏、色情、暴力或其他不当内容，请回复"deny"，否则回复"allow"。'
                },
                'vision_audit': {
                    'enabled': True,
                    'focus_time_trigger_minutes': 30,
                    'provider': 'openai',  # 'openai' or 'gemini'
                    'api_key': '',
                    'model': 'gpt-4-vision-preview',
                    'prompt_template': '请将此截图中的应用分类为：游戏、浏览器、工具、聊天软件、办公软件、其他。只回复分类名称。'
                }
            },
            'uninstall_window': {
                'enabled': True,
                'day_of_week': 0,  # 0=周一, 6=周日
                'start_time': '02:00',
                'end_time': '04:00'
            },
            # 向后兼容字段
            'blacklist_rules': []  # 保留用于迁移旧配置
        }
    
    def get_blacklist_rules(self) -> List[str]:
        """
        获取黑名单规则列表

        Returns:
            List[str]: 黑名单规则列表
        """
        try:
            config = self.load_config()
            return config.get('blacklist_rules', [])
        except Exception as e:
            print(f"获取黑名单规则失败: {str(e)}")
            return []

    def save_blacklist_rules(self, rules: List[str]) -> None:
        """
        保存黑名单规则列表

        Args:
            rules: 黑名单规则列表
        """
        try:
            config = self.load_config()
            config['blacklist_rules'] = rules
            self.save_config(config)
        except Exception as e:
            raise Exception(f"保存黑名单规则失败: {str(e)}")

    def add_blacklist_rule(self, rule: str) -> None:
        """
        添加黑名单规则

        Args:
            rule: 要添加的规则
        """
        if not rule.strip():
            raise ValueError("规则不能为空")

        rules = self.get_blacklist_rules()
        if rule in rules:
            raise ValueError("规则已存在")

        rules.append(rule)
        self.save_blacklist_rules(rules)

    def remove_blacklist_rule(self, rule: str) -> None:
        """
        删除黑名单规则

        Args:
            rule: 要删除的规则
        """
        rules = self.get_blacklist_rules()
        if rule not in rules:
            raise ValueError("规则不存在")

        rules.remove(rule)
        self.save_blacklist_rules(rules)

    def verify_blacklist_integrity(self) -> bool:
        """
        验证黑名单完整性（由于现在存储在加密配置中，始终返回True）

        Returns:
            bool: 始终返回True，保持向后兼容
        """
        return True

    # ==================== 融合版新增配置管理方法 ====================

    def get_feature_config(self, feature_name: str) -> bool:
        """获取功能开关状态"""
        try:
            config = self.load_config()
            return config.get('features', {}).get(feature_name, False)
        except Exception as e:
            print(f"获取功能配置失败: {str(e)}")
            return False

    def set_feature_config(self, feature_name: str, enabled: bool) -> None:
        """设置功能开关状态"""
        try:
            config = self.load_config()
            if 'features' not in config:
                config['features'] = {}
            config['features'][feature_name] = enabled
            self.save_config(config)
        except Exception as e:
            raise Exception(f"设置功能配置失败: {str(e)}")

    def get_ai_config(self, ai_type: str) -> Dict[str, Any]:
        """获取AI配置（text_audit 或 vision_audit）"""
        try:
            config = self.load_config()
            return config.get('ai_config', {}).get(ai_type, {})
        except Exception as e:
            print(f"获取AI配置失败: {str(e)}")
            return {}

    def set_ai_config(self, ai_type: str, ai_config: Dict[str, Any]) -> None:
        """设置AI配置"""
        try:
            config = self.load_config()
            if 'ai_config' not in config:
                config['ai_config'] = {}
            config['ai_config'][ai_type] = ai_config
            self.save_config(config)
        except Exception as e:
            raise Exception(f"设置AI配置失败: {str(e)}")

    def get_time_limit_rules(self) -> Dict[str, Dict[str, Any]]:
        """获取时间限制规则"""
        try:
            config = self.load_config()
            return config.get('rules', {}).get('time_limit_rules', {})
        except Exception as e:
            print(f"获取时间限制规则失败: {str(e)}")
            return {}

    def set_time_limit_rule(self, category: str, limit_minutes: int, enabled: bool = True) -> None:
        """设置分类的时间限制规则"""
        try:
            config = self.load_config()
            if 'rules' not in config:
                config['rules'] = {}
            if 'time_limit_rules' not in config['rules']:
                config['rules']['time_limit_rules'] = {}

            config['rules']['time_limit_rules'][category] = {
                'limit_minutes_per_day': limit_minutes,
                'enabled': enabled
            }
            self.save_config(config)
        except Exception as e:
            raise Exception(f"设置时间限制规则失败: {str(e)}")

    def get_browser_content_blacklist(self) -> Dict[str, List[str]]:
        """获取浏览器内容黑名单"""
        try:
            config = self.load_config()
            return config.get('rules', {}).get('browser_content_blacklist', {'keywords': [], 'urls': []})
        except Exception as e:
            print(f"获取浏览器内容黑名单失败: {str(e)}")
            return {'keywords': [], 'urls': []}

    def add_browser_content_blacklist(self, content_type: str, value: str) -> None:
        """添加浏览器内容黑名单项（keywords 或 urls）"""
        try:
            config = self.load_config()
            if 'rules' not in config:
                config['rules'] = {}
            if 'browser_content_blacklist' not in config['rules']:
                config['rules']['browser_content_blacklist'] = {'keywords': [], 'urls': []}

            if content_type not in config['rules']['browser_content_blacklist']:
                config['rules']['browser_content_blacklist'][content_type] = []

            if value not in config['rules']['browser_content_blacklist'][content_type]:
                config['rules']['browser_content_blacklist'][content_type].append(value)
                self.save_config(config)
            else:
                raise ValueError(f"{content_type}黑名单项已存在")
        except Exception as e:
            raise Exception(f"添加浏览器内容黑名单失败: {str(e)}")

    def remove_browser_content_blacklist(self, content_type: str, value: str) -> None:
        """删除浏览器内容黑名单项"""
        try:
            config = self.load_config()
            blacklist = config.get('rules', {}).get('browser_content_blacklist', {})

            if content_type in blacklist and value in blacklist[content_type]:
                blacklist[content_type].remove(value)
                self.save_config(config)
            else:
                raise ValueError(f"{content_type}黑名单项不存在")
        except Exception as e:
            raise Exception(f"删除浏览器内容黑名单失败: {str(e)}")

    def migrate_legacy_config(self) -> None:
        """迁移旧配置到新的分层结构"""
        try:
            config = self.load_config()
            migrated = False

            # 迁移旧的blacklist_rules到新的process_blacklist
            if 'blacklist_rules' in config and config['blacklist_rules']:
                if 'rules' not in config:
                    config['rules'] = {}
                if 'process_blacklist' not in config['rules']:
                    config['rules']['process_blacklist'] = []

                # 合并旧规则到新结构
                for rule in config['blacklist_rules']:
                    if rule not in config['rules']['process_blacklist']:
                        config['rules']['process_blacklist'].append(rule)

                # 清空旧字段但保留以防回滚
                config['blacklist_rules'] = []
                migrated = True

            if migrated:
                self.save_config(config)
                print("配置迁移完成")

        except Exception as e:
            print(f"配置迁移失败: {str(e)}")

# 全局配置处理器实例
_config_handler = ConfigHandler()

def load_config() -> Dict[str, Any]:
    """加载配置"""
    return _config_handler.load_config()

def save_config(config_data: Dict[str, Any]) -> None:
    """保存配置"""
    _config_handler.save_config(config_data)

def get_blacklist_rules() -> List[str]:
    """获取黑名单规则"""
    return _config_handler.get_blacklist_rules()

def save_blacklist_rules(rules: List[str]) -> None:
    """保存黑名单规则"""
    _config_handler.save_blacklist_rules(rules)

def add_blacklist_rule(rule: str) -> None:
    """添加黑名单规则"""
    _config_handler.add_blacklist_rule(rule)

def remove_blacklist_rule(rule: str) -> None:
    """删除黑名单规则"""
    _config_handler.remove_blacklist_rule(rule)

def verify_blacklist_integrity() -> bool:
    """验证黑名单完整性"""
    return _config_handler.verify_blacklist_integrity()

# ==================== 融合版新增全局函数 ====================

def get_feature_config(feature_name: str) -> bool:
    """获取功能开关状态"""
    return _config_handler.get_feature_config(feature_name)

def set_feature_config(feature_name: str, enabled: bool) -> None:
    """设置功能开关状态"""
    _config_handler.set_feature_config(feature_name, enabled)

def get_ai_config(ai_type: str) -> Dict[str, Any]:
    """获取AI配置"""
    return _config_handler.get_ai_config(ai_type)

def set_ai_config(ai_type: str, ai_config: Dict[str, Any]) -> None:
    """设置AI配置"""
    _config_handler.set_ai_config(ai_type, ai_config)

def get_time_limit_rules() -> Dict[str, Dict[str, Any]]:
    """获取时间限制规则"""
    return _config_handler.get_time_limit_rules()

def set_time_limit_rule(category: str, limit_minutes: int, enabled: bool = True) -> None:
    """设置分类的时间限制规则"""
    _config_handler.set_time_limit_rule(category, limit_minutes, enabled)

def get_process_blacklist() -> List[str]:
    """获取进程黑名单（融合蓝图新规定的结构）"""
    try:
        config = _config_handler.load_config()
        return config.get('rules', {}).get('process_blacklist', [])
    except Exception as e:
        print(f"获取进程黑名单失败: {str(e)}")
        return []

def get_allowed_browsers() -> List[str]:
    """获取允许的浏览器列表"""
    try:
        config = _config_handler.load_config()
        return config.get('rules', {}).get('allowed_browsers', ['chrome', 'msedge', 'firefox'])
    except Exception as e:
        print(f"获取允许浏览器列表失败: {str(e)}")
        return ['chrome', 'msedge', 'firefox']

def get_browser_content_blacklist() -> Dict[str, List[str]]:
    """获取浏览器内容黑名单"""
    return _config_handler.get_browser_content_blacklist()

def add_browser_content_blacklist(content_type: str, value: str) -> None:
    """添加浏览器内容黑名单项"""
    _config_handler.add_browser_content_blacklist(content_type, value)

def remove_browser_content_blacklist(content_type: str, value: str) -> None:
    """删除浏览器内容黑名单项"""
    _config_handler.remove_browser_content_blacklist(content_type, value)

def migrate_legacy_config() -> None:
    """迁移旧配置到新的分层结构"""
    _config_handler.migrate_legacy_config()
