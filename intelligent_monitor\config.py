# 智能监控系统配置文件 (简化版)
# 此文件仅包含intelligent_monitor模块内部使用的配置

# --- 调试开关 ---
DEBUG_BLACKLIST = False  # 关闭调试以减少日志
DEBUG_CACHE = False
DEBUG_LIFECYCLE = False

# --- 生命周期管理参数 ---
EXPLORATION_BUDGET = 5  # 探索阶段最大尝试次数
HARD_FAILURE_THRESHOLD = 3  # 稳定阶段硬失效阈值
FULL_SEARCH_INTERVAL = 60  # 全量搜索间隔（秒）
CACHE_FILE_PATH = "browser_strategy_cache.json"  # 缓存文件路径

# --- 枚举类定义 ---
class StrategyPhase:
    """策略生命周期阶段"""
    EXPLORATION = "exploration"  # 探索阶段
    STABLE = "stable"           # 稳定阶段
    ABANDONED = "abandoned"     # 放弃阶段
    FAILED = "failed"          # 失效阶段

class StrategyQuality:
    """策略质量等级"""
    NONE = "none"    # 无效（硬失效）
    LOW = "low"      # 次优（软失效）
    HIGH = "high"    # 高质量

class FailureType:
    """失效类型"""
    HARD = "hard"    # 硬失效（连标题都获取不到）
    SOFT = "soft"    # 软失效（有标题无URL）

# --- AI审核配置 ---
AI_AUDIT_CONFIG = {
    "enabled": True,
    "cache_file_path": "ai_audit_cache.json",
    "cache_ttl_seconds": 3600,  # 1小时
    "api_key": "",  # 将从环境变量或主配置读取
    "api_endpoint": "https://openrouter.ai/api/v1/chat/completions",
    "model": "google/gemma-3-27b-it:free",
    "prompt_template": "作为一名网络内容审核员，请判断以下网页内容是否合规。标题：{title}，URL：{url}。如果内容涉及游戏、色情、暴力或其他不当内容，请回复'deny'，否则回复'allow'。"
}
