Date : 2025-07-16 21:05:55
Directory : d:\MyProjects\Selfdiscipline\Combination
Total : 60 files,  67887 codes, 1545 comments, 3158 blanks, all 72590 lines

Languages
+------------+------------+------------+------------+------------+------------+
| language   | files      | code       | comment    | blank      | total      |
+------------+------------+------------+------------+------------+------------+
| XML        |         16 |     60,218 |          0 |      1,377 |     61,595 |
| Python     |         30 |      5,508 |      1,479 |      1,309 |      8,296 |
| Markdown   |          8 |      1,181 |          6 |        334 |      1,521 |
| JavaScript |          1 |        429 |         31 |         58 |        518 |
| HTML       |          1 |        268 |         11 |         12 |        291 |
| PostCSS    |          1 |        267 |         18 |         64 |        349 |
| PowerShell |          2 |         13 |          0 |          4 |         17 |
| JSON       |          1 |          3 |          0 |          0 |          3 |
+------------+------------+------------+------------+------------+------------+

Directories
+--------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                                                                     | files      | code       | comment    | blank      | total      |
+--------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                                                                        |         60 |     67,887 |      1,545 |      3,158 |     72,590 |
| . (Files)                                                                                                                |          7 |      1,201 |         90 |        330 |      1,621 |
| Interop.UIAutomationClient.10.19041.0                                                                                    |          2 |         16 |          0 |          2 |         18 |
| Interop.UIAutomationClient.10.19041.0\build                                                                              |          1 |         13 |          0 |          1 |         14 |
| Interop.UIAutomationClient.10.19041.0\tools                                                                              |          1 |          3 |          0 |          1 |          4 |
| System.CodeDom.8.0.0                                                                                                     |          7 |     22,022 |          0 |        262 |     22,284 |
| System.CodeDom.8.0.0\buildTransitive                                                                                     |          2 |         12 |          0 |          2 |         14 |
| System.CodeDom.8.0.0\buildTransitive\net461                                                                              |          1 |          6 |          0 |          1 |          7 |
| System.CodeDom.8.0.0\buildTransitive\netcoreapp2.0                                                                       |          1 |          6 |          0 |          1 |          7 |
| System.CodeDom.8.0.0\lib                                                                                                 |          5 |     22,010 |          0 |        260 |     22,270 |
| System.CodeDom.8.0.0\lib\net462                                                                                          |          1 |      4,402 |          0 |         52 |      4,454 |
| System.CodeDom.8.0.0\lib\net6.0                                                                                          |          1 |      4,402 |          0 |         52 |      4,454 |
| System.CodeDom.8.0.0\lib\net7.0                                                                                          |          1 |      4,402 |          0 |         52 |      4,454 |
| System.CodeDom.8.0.0\lib\net8.0                                                                                          |          1 |      4,402 |          0 |         52 |      4,454 |
| System.CodeDom.8.0.0\lib\netstandard2.0                                                                                  |          1 |      4,402 |          0 |         52 |      4,454 |
| System.Management.8.0.0                                                                                                  |          9 |     38,224 |          6 |      1,138 |     39,368 |
| System.Management.8.0.0 (Files)                                                                                          |          1 |         41 |          6 |         24 |         71 |
| System.Management.8.0.0\buildTransitive                                                                                  |          1 |          6 |          0 |          1 |          7 |
| System.Management.8.0.0\buildTransitive\netcoreapp2.0                                                                    |          1 |          6 |          0 |          1 |          7 |
| System.Management.8.0.0\lib                                                                                              |          4 |     10,116 |          0 |          0 |     10,116 |
| System.Management.8.0.0\lib\net6.0                                                                                       |          1 |      2,529 |          0 |          0 |      2,529 |
| System.Management.8.0.0\lib\net7.0                                                                                       |          1 |      2,529 |          0 |          0 |      2,529 |
| System.Management.8.0.0\lib\net8.0                                                                                       |          1 |      2,529 |          0 |          0 |      2,529 |
| System.Management.8.0.0\lib\netstandard2.0                                                                               |          1 |      2,529 |          0 |          0 |      2,529 |
| System.Management.8.0.0\runtimes                                                                                         |          3 |     28,061 |          0 |      1,113 |     29,174 |
| System.Management.8.0.0\runtimes\win                                                                                     |          3 |     28,061 |          0 |      1,113 |     29,174 |
| System.Management.8.0.0\runtimes\win\lib                                                                                 |          3 |     28,061 |          0 |      1,113 |     29,174 |
| System.Management.8.0.0\runtimes\win\lib\net6.0                                                                          |          1 |      9,401 |          0 |        371 |      9,772 |
| System.Management.8.0.0\runtimes\win\lib\net7.0                                                                          |          1 |      9,330 |          0 |        371 |      9,701 |
| System.Management.8.0.0\runtimes\win\lib\net8.0                                                                          |          1 |      9,330 |          0 |        371 |      9,701 |
| UIAComWrapper.********                                                                                                   |          1 |         10 |          0 |          3 |         13 |
| UIAComWrapper.********\tools                                                                                             |          1 |         10 |          0 |          3 |         13 |
| backend                                                                                                                  |          2 |        929 |        235 |        261 |      1,425 |
| common                                                                                                                   |          9 |      1,569 |        700 |        410 |      2,679 |
| frontend                                                                                                                 |          5 |      1,666 |        153 |        262 |      2,081 |
| frontend (Files)                                                                                                         |          2 |        702 |         93 |        128 |        923 |
| frontend\static                                                                                                          |          2 |        696 |         49 |        122 |        867 |
| frontend\static\css                                                                                                      |          1 |        267 |         18 |         64 |        349 |
| frontend\static\js                                                                                                       |          1 |        429 |         31 |         58 |        518 |
| frontend\templates                                                                                                       |          1 |        268 |         11 |         12 |        291 |
| installer                                                                                                                |          1 |        310 |         76 |         93 |        479 |
| intelligent_monitor                                                                                                      |         15 |      1,721 |        255 |        352 |      2,328 |
| intelligent_monitor (Files)                                                                                              |          8 |      1,035 |        102 |        226 |      1,363 |
| intelligent_monitor\core                                                                                                 |          2 |         75 |         16 |         12 |        103 |
| intelligent_monitor\modules                                                                                              |          5 |        611 |        137 |        114 |        862 |
| proxy                                                                                                                    |          2 |        219 |         30 |         45 |        294 |
+--------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+--------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| filename                                                                                                                 | language   | code       | comment    | blank      | total      |
+--------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| d:\MyProjects\Selfdiscipline\Combination\DEPLOYMENT_GUIDE.md                                                             | Markdown   |        197 |          0 |         45 |        242 |
| d:\MyProjects\Selfdiscipline\Combination\FUSION_SUMMARY.md                                                               | Markdown   |        138 |          0 |         36 |        174 |
| d:\MyProjects\Selfdiscipline\Combination\Interop.UIAutomationClient.10.19041.0\build\Interop.UIAutomationClient.targets  | XML        |         13 |          0 |          1 |         14 |
| d:\MyProjects\Selfdiscipline\Combination\Interop.UIAutomationClient.10.19041.0\tools\install.ps1                         | PowerShell |          3 |          0 |          1 |          4 |
| d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\buildTransitive\net461\System.CodeDom.targets              | XML        |          6 |          0 |          1 |          7 |
| d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\buildTransitive\netcoreapp2.0\System.CodeDom.targets       | XML        |          6 |          0 |          1 |          7 |
| d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\lib\net462\System.CodeDom.xml                              | XML        |      4,402 |          0 |         52 |      4,454 |
| d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\lib\net6.0\System.CodeDom.xml                              | XML        |      4,402 |          0 |         52 |      4,454 |
| d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\lib\net7.0\System.CodeDom.xml                              | XML        |      4,402 |          0 |         52 |      4,454 |
| d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\lib\net8.0\System.CodeDom.xml                              | XML        |      4,402 |          0 |         52 |      4,454 |
| d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\lib\netstandard2.0\System.CodeDom.xml                      | XML        |      4,402 |          0 |         52 |      4,454 |
| d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\PACKAGE.md                                              | Markdown   |         41 |          6 |         24 |         71 |
| d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\buildTransitive\netcoreapp2.0\System.Management.targets | XML        |          6 |          0 |          1 |          7 |
| d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\lib\net6.0\System.Management.xml                        | XML        |      2,529 |          0 |          0 |      2,529 |
| d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\lib\net7.0\System.Management.xml                        | XML        |      2,529 |          0 |          0 |      2,529 |
| d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\lib\net8.0\System.Management.xml                        | XML        |      2,529 |          0 |          0 |      2,529 |
| d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\lib\netstandard2.0\System.Management.xml                | XML        |      2,529 |          0 |          0 |      2,529 |
| d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\runtimes\win\lib\net6.0\System.Management.xml           | XML        |      9,401 |          0 |        371 |      9,772 |
| d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\runtimes\win\lib\net7.0\System.Management.xml           | XML        |      9,330 |          0 |        371 |      9,701 |
| d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\runtimes\win\lib\net8.0\System.Management.xml           | XML        |      9,330 |          0 |        371 |      9,701 |
| d:\MyProjects\Selfdiscipline\Combination\UIAComWrapper.********\tools\install.ps1                                        | PowerShell |         10 |          0 |          3 |         13 |
| d:\MyProjects\Selfdiscipline\Combination\backend\guardian_service.py                                                     | Python     |        342 |         98 |         98 |        538 |
| d:\MyProjects\Selfdiscipline\Combination\backend\unified_guardian_service.py                                             | Python     |        587 |        137 |        163 |        887 |
| d:\MyProjects\Selfdiscipline\Combination\build_script.py                                                                 | Python     |        230 |         39 |         60 |        329 |
| d:\MyProjects\Selfdiscipline\Combination\common\__init__.py                                                              | Python     |         44 |         14 |         10 |         68 |
| d:\MyProjects\Selfdiscipline\Combination\common\ai_manager.py                                                            | Python     |        188 |         56 |         56 |        300 |
| d:\MyProjects\Selfdiscipline\Combination\common\config_handler.py                                                        | Python     |        303 |        114 |         68 |        485 |
| d:\MyProjects\Selfdiscipline\Combination\common\cryption.py                                                              | Python     |         48 |         57 |         19 |        124 |
| d:\MyProjects\Selfdiscipline\Combination\common\database_manager.py                                                      | Python     |        254 |         34 |         69 |        357 |
| d:\MyProjects\Selfdiscipline\Combination\common\notification_manager.py                                                  | Python     |        168 |         75 |         51 |        294 |
| d:\MyProjects\Selfdiscipline\Combination\common\process_utils.py                                                         | Python     |        136 |         76 |         37 |        249 |
| d:\MyProjects\Selfdiscipline\Combination\common\system_utils.py                                                          | Python     |        315 |        203 |         68 |        586 |
| d:\MyProjects\Selfdiscipline\Combination\common\time_utils.py                                                            | Python     |        113 |         71 |         32 |        216 |
| d:\MyProjects\Selfdiscipline\Combination\frontend\launcher.py                                                            | Python     |         67 |         25 |         25 |        117 |
| d:\MyProjects\Selfdiscipline\Combination\frontend\static\css\style.css                                                   | PostCSS    |        267 |         18 |         64 |        349 |
| d:\MyProjects\Selfdiscipline\Combination\frontend\static\js\app.js                                                       | JavaScript |        429 |         31 |         58 |        518 |
| d:\MyProjects\Selfdiscipline\Combination\frontend\templates\index.html                                                   | HTML       |        268 |         11 |         12 |        291 |
| d:\MyProjects\Selfdiscipline\Combination\frontend\web_control_panel.py                                                   | Python     |        635 |         68 |        103 |        806 |
| d:\MyProjects\Selfdiscipline\Combination\installer\setup.py                                                              | Python     |        310 |         76 |         93 |        479 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\README.md                                                   | Markdown   |        101 |          0 |         29 |        130 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\REFACTORING_SUMMARY.md                                      | Markdown   |        115 |          0 |         32 |        147 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\__init__.py                                                 | Python     |          0 |          1 |          1 |          2 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\config.py                                                   | Python     |         47 |         33 |          7 |         87 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\core\__init__.py                                            | Python     |          0 |          1 |          1 |          2 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\core\uia_base.py                                            | Python     |         75 |         15 |         11 |        101 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\gui.py                                                      | Python     |        283 |         23 |         50 |        356 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\introduce.md                                                | Markdown   |        215 |          0 |         45 |        260 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\main.py                                                     | Python     |        156 |         27 |         36 |        219 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\modules\__init__.py                                         | Python     |          0 |          1 |          1 |          2 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\modules\blacklist_checker.py                                | Python     |         23 |          6 |          5 |         34 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\modules\browser_info_fetcher.py                             | Python     |        130 |         23 |         30 |        183 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\modules\managers.py                                         | Python     |        279 |         68 |         33 |        380 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\modules\tab_closer.py                                       | Python     |        179 |         39 |         45 |        263 |
| d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\test_modules.py                                             | Python     |        118 |         18 |         26 |        162 |
| d:\MyProjects\Selfdiscipline\Combination\proxy\requesttest.py                                                            | Python     |        216 |         30 |         45 |        291 |
| d:\MyProjects\Selfdiscipline\Combination\proxy\user_agent_cache.json                                                     | JSON       |          3 |          0 |          0 |          3 |
| d:\MyProjects\Selfdiscipline\Combination\test_fusion.py                                                                  | Python     |        165 |         34 |         46 |        245 |
| d:\MyProjects\Selfdiscipline\Combination\uninstall.py                                                                    | Python     |         97 |         17 |         20 |        134 |
| d:\MyProjects\Selfdiscipline\Combination\融合蓝图.md                                                                         | Markdown   |        178 |          0 |         40 |        218 |
| d:\MyProjects\Selfdiscipline\Combination\项目蓝图.md                                                                         | Markdown   |        196 |          0 |         83 |        279 |
| Total                                                                                                                    |            |     67,887 |      1,545 |      3,158 |     72,590 |
+--------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+