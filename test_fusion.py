"""
融合版测试脚本 - 自律守护者 v7.0
测试各个模块的基本功能
"""

import os
import sys
import time
import tempfile
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'common'))
sys.path.insert(0, str(project_root / 'intelligent_monitor'))

def test_config_handler():
    """测试配置处理器"""
    print("=== 测试配置处理器 ===")
    try:
        import common.config_handler as config_handler
        
        # 测试加载默认配置
        config = config_handler.load_config()
        print(f"✓ 配置加载成功，版本: {config.get('version')}")
        
        # 测试功能开关
        config_handler.set_feature_config('enable_global_process_scan', True)
        enabled = config_handler.get_feature_config('enable_global_process_scan')
        print(f"✓ 功能开关测试成功: {enabled}")
        
        # 测试AI配置
        ai_config = {
            'enabled': True,
            'provider': 'openai',
            'api_key': 'test_key'
        }
        config_handler.set_ai_config('text_audit', ai_config)
        loaded_ai_config = config_handler.get_ai_config('text_audit')
        print(f"✓ AI配置测试成功: {loaded_ai_config.get('provider')}")
        
        return True
    except Exception as e:
        print(f"✗ 配置处理器测试失败: {str(e)}")
        return False

def test_database_manager():
    """测试数据库管理器"""
    print("\n=== 测试数据库管理器 ===")
    try:
        from common.database_manager import DatabaseManager
        
        # 使用临时数据库
        temp_db = tempfile.mktemp(suffix='.db')
        db_manager = DatabaseManager(temp_db)
        
        # 测试应用分类
        db_manager.set_app_category('test.exe', '游戏')
        category = db_manager.get_app_category('test.exe')
        print(f"✓ 应用分类测试成功: {category}")
        
        # 测试专注时间日志
        success = db_manager.add_focus_log('test.exe', 'Test Window', duration_seconds=300)
        print(f"✓ 专注时间日志测试成功: {success}")
        
        # 测试临时白名单
        db_manager.add_to_temp_whitelist('url', 'http://test.com', 1)
        in_whitelist = db_manager.is_in_temp_whitelist('url', 'http://test.com')
        print(f"✓ 临时白名单测试成功: {in_whitelist}")
        
        # 测试统计信息
        stats = db_manager.get_database_stats()
        print(f"✓ 数据库统计测试成功: {stats}")
        
        # 清理
        os.unlink(temp_db)
        return True
    except Exception as e:
        print(f"✗ 数据库管理器测试失败: {str(e)}")
        return False

def test_ai_manager():
    """测试AI管理器"""
    print("\n=== 测试AI管理器 ===")
    try:
        from common.ai_manager import AIManager
        
        ai_manager = AIManager()
        
        # 测试文本审核（模拟模式）
        result = ai_manager.audit_text('http://test.com', 'Test Page')
        print(f"✓ 文本审核测试成功: {result}")
        
        # 测试图像分类（模拟模式）
        test_image = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
        category = ai_manager.classify_image(test_image)
        print(f"✓ 图像分类测试成功: {category}")
        
        # 测试缓存统计
        stats = ai_manager.get_cache_stats()
        print(f"✓ AI缓存统计测试成功: {stats}")
        
        return True
    except Exception as e:
        print(f"✗ AI管理器测试失败: {str(e)}")
        return False

def test_notification_manager():
    """测试通知管理器"""
    print("\n=== 测试通知管理器 ===")
    try:
        from common.notification_manager import NotificationManager
        from common.database_manager import DatabaseManager
        
        # 使用临时数据库
        temp_db = tempfile.mktemp(suffix='.db')
        db_manager = DatabaseManager(temp_db)
        notification_manager = NotificationManager(db_manager)
        
        print("✓ 通知管理器初始化成功")
        
        # 清理
        notification_manager.cleanup()
        os.unlink(temp_db)
        return True
    except Exception as e:
        print(f"✗ 通知管理器测试失败: {str(e)}")
        return False

def test_system_utils():
    """测试系统工具"""
    print("\n=== 测试系统工具 ===")
    try:
        import common.system_utils as system_utils
        
        # 测试系统名称生成
        utils = system_utils.SystemUtils()
        name = utils.generate_system_name('Test')
        print(f"✓ 系统名称生成测试成功: {name}")
        
        # 测试截图功能（可能失败，因为需要GUI环境）
        try:
            screenshot = utils.take_screenshot()
            if screenshot:
                print(f"✓ 截图功能测试成功: {len(screenshot)} bytes")
            else:
                print("⚠ 截图功能测试跳过（无GUI环境）")
        except Exception:
            print("⚠ 截图功能测试跳过（依赖缺失）")
        
        return True
    except Exception as e:
        print(f"✗ 系统工具测试失败: {str(e)}")
        return False

def test_intelligent_monitor():
    """测试智能监控模块"""
    print("\n=== 测试智能监控模块 ===")
    try:
        # 测试UIA基础模块
        from intelligent_monitor.core import uia_base
        print(f"✓ UIA模块加载状态: {uia_base.UIA_LOADED}")
        
        # 测试配置模块
        from intelligent_monitor import config as monitor_config
        print(f"✓ 监控配置加载成功: {len(monitor_config.ALLOWED_BROWSERS)} 个允许的浏览器")
        
        # 测试管理器模块
        if uia_base.UIA_LOADED:
            from intelligent_monitor.modules.managers import StrategyLifecycleManager, AIAuditManager
            
            lifecycle_manager = StrategyLifecycleManager()
            ai_audit_manager = AIAuditManager()
            print("✓ 智能监控管理器初始化成功")
        else:
            print("⚠ UIA组件未加载，跳过管理器测试")
        
        return True
    except Exception as e:
        print(f"✗ 智能监控模块测试失败: {str(e)}")
        return False

def test_web_control_panel():
    """测试Web控制面板"""
    print("\n=== 测试Web控制面板 ===")
    try:
        from frontend.web_control_panel import WebControlPanel
        
        panel = WebControlPanel()
        print("✓ Web控制面板初始化成功")
        
        # 测试Flask应用
        with panel.app.test_client() as client:
            response = client.get('/')
            print(f"✓ 主页访问测试成功: {response.status_code}")
            
            # 测试API
            response = client.get('/api/features')
            print(f"✓ 功能API测试成功: {response.status_code}")
        
        return True
    except Exception as e:
        print(f"✗ Web控制面板测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("自律守护者 v7.0 融合版 - 功能测试")
    print("=" * 50)
    
    tests = [
        test_config_handler,
        test_database_manager,
        test_ai_manager,
        test_notification_manager,
        test_system_utils,
        test_intelligent_monitor,
        test_web_control_panel
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！融合版基本功能正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
