"filename", "language", "Python", "Markdown", "PowerShell", "XML", "JSON", "PostCSS", "JavaScript", "HTML", "comment", "blank", "total"
"d:\MyProjects\Selfdiscipline\Combination\DEPLOYMENT_GUIDE.md", "Markdown", 0, 197, 0, 0, 0, 0, 0, 0, 0, 45, 242
"d:\MyProjects\Selfdiscipline\Combination\FUSION_SUMMARY.md", "Markdown", 0, 138, 0, 0, 0, 0, 0, 0, 0, 36, 174
"d:\MyProjects\Selfdiscipline\Combination\Interop.UIAutomationClient.10.19041.0\build\Interop.UIAutomationClient.targets", "XML", 0, 0, 0, 13, 0, 0, 0, 0, 0, 1, 14
"d:\MyProjects\Selfdiscipline\Combination\Interop.UIAutomationClient.10.19041.0\tools\install.ps1", "PowerShell", 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\buildTransitive\net461\System.CodeDom.targets", "XML", 0, 0, 0, 6, 0, 0, 0, 0, 0, 1, 7
"d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\buildTransitive\netcoreapp2.0\System.CodeDom.targets", "XML", 0, 0, 0, 6, 0, 0, 0, 0, 0, 1, 7
"d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\lib\net462\System.CodeDom.xml", "XML", 0, 0, 0, 4402, 0, 0, 0, 0, 0, 52, 4454
"d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\lib\net6.0\System.CodeDom.xml", "XML", 0, 0, 0, 4402, 0, 0, 0, 0, 0, 52, 4454
"d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\lib\net7.0\System.CodeDom.xml", "XML", 0, 0, 0, 4402, 0, 0, 0, 0, 0, 52, 4454
"d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\lib\net8.0\System.CodeDom.xml", "XML", 0, 0, 0, 4402, 0, 0, 0, 0, 0, 52, 4454
"d:\MyProjects\Selfdiscipline\Combination\System.CodeDom.8.0.0\lib\netstandard2.0\System.CodeDom.xml", "XML", 0, 0, 0, 4402, 0, 0, 0, 0, 0, 52, 4454
"d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\PACKAGE.md", "Markdown", 0, 41, 0, 0, 0, 0, 0, 0, 6, 24, 71
"d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\buildTransitive\netcoreapp2.0\System.Management.targets", "XML", 0, 0, 0, 6, 0, 0, 0, 0, 0, 1, 7
"d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\lib\net6.0\System.Management.xml", "XML", 0, 0, 0, 2529, 0, 0, 0, 0, 0, 0, 2529
"d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\lib\net7.0\System.Management.xml", "XML", 0, 0, 0, 2529, 0, 0, 0, 0, 0, 0, 2529
"d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\lib\net8.0\System.Management.xml", "XML", 0, 0, 0, 2529, 0, 0, 0, 0, 0, 0, 2529
"d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\lib\netstandard2.0\System.Management.xml", "XML", 0, 0, 0, 2529, 0, 0, 0, 0, 0, 0, 2529
"d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\runtimes\win\lib\net6.0\System.Management.xml", "XML", 0, 0, 0, 9401, 0, 0, 0, 0, 0, 371, 9772
"d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\runtimes\win\lib\net7.0\System.Management.xml", "XML", 0, 0, 0, 9330, 0, 0, 0, 0, 0, 371, 9701
"d:\MyProjects\Selfdiscipline\Combination\System.Management.8.0.0\runtimes\win\lib\net8.0\System.Management.xml", "XML", 0, 0, 0, 9330, 0, 0, 0, 0, 0, 371, 9701
"d:\MyProjects\Selfdiscipline\Combination\UIAComWrapper.********\tools\install.ps1", "PowerShell", 0, 0, 10, 0, 0, 0, 0, 0, 0, 3, 13
"d:\MyProjects\Selfdiscipline\Combination\backend\guardian_service.py", "Python", 342, 0, 0, 0, 0, 0, 0, 0, 98, 98, 538
"d:\MyProjects\Selfdiscipline\Combination\backend\unified_guardian_service.py", "Python", 587, 0, 0, 0, 0, 0, 0, 0, 137, 163, 887
"d:\MyProjects\Selfdiscipline\Combination\build_script.py", "Python", 230, 0, 0, 0, 0, 0, 0, 0, 39, 60, 329
"d:\MyProjects\Selfdiscipline\Combination\common\__init__.py", "Python", 44, 0, 0, 0, 0, 0, 0, 0, 14, 10, 68
"d:\MyProjects\Selfdiscipline\Combination\common\ai_manager.py", "Python", 188, 0, 0, 0, 0, 0, 0, 0, 56, 56, 300
"d:\MyProjects\Selfdiscipline\Combination\common\config_handler.py", "Python", 303, 0, 0, 0, 0, 0, 0, 0, 114, 68, 485
"d:\MyProjects\Selfdiscipline\Combination\common\cryption.py", "Python", 48, 0, 0, 0, 0, 0, 0, 0, 57, 19, 124
"d:\MyProjects\Selfdiscipline\Combination\common\database_manager.py", "Python", 254, 0, 0, 0, 0, 0, 0, 0, 34, 69, 357
"d:\MyProjects\Selfdiscipline\Combination\common\notification_manager.py", "Python", 168, 0, 0, 0, 0, 0, 0, 0, 75, 51, 294
"d:\MyProjects\Selfdiscipline\Combination\common\process_utils.py", "Python", 136, 0, 0, 0, 0, 0, 0, 0, 76, 37, 249
"d:\MyProjects\Selfdiscipline\Combination\common\system_utils.py", "Python", 315, 0, 0, 0, 0, 0, 0, 0, 203, 68, 586
"d:\MyProjects\Selfdiscipline\Combination\common\time_utils.py", "Python", 113, 0, 0, 0, 0, 0, 0, 0, 71, 32, 216
"d:\MyProjects\Selfdiscipline\Combination\frontend\launcher.py", "Python", 67, 0, 0, 0, 0, 0, 0, 0, 25, 25, 117
"d:\MyProjects\Selfdiscipline\Combination\frontend\static\css\style.css", "PostCSS", 0, 0, 0, 0, 0, 267, 0, 0, 18, 64, 349
"d:\MyProjects\Selfdiscipline\Combination\frontend\static\js\app.js", "JavaScript", 0, 0, 0, 0, 0, 0, 429, 0, 31, 58, 518
"d:\MyProjects\Selfdiscipline\Combination\frontend\templates\index.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 268, 11, 12, 291
"d:\MyProjects\Selfdiscipline\Combination\frontend\web_control_panel.py", "Python", 635, 0, 0, 0, 0, 0, 0, 0, 68, 103, 806
"d:\MyProjects\Selfdiscipline\Combination\installer\setup.py", "Python", 310, 0, 0, 0, 0, 0, 0, 0, 76, 93, 479
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\README.md", "Markdown", 0, 101, 0, 0, 0, 0, 0, 0, 0, 29, 130
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\REFACTORING_SUMMARY.md", "Markdown", 0, 115, 0, 0, 0, 0, 0, 0, 0, 32, 147
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\__init__.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 2
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\config.py", "Python", 47, 0, 0, 0, 0, 0, 0, 0, 33, 7, 87
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\core\__init__.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 2
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\core\uia_base.py", "Python", 75, 0, 0, 0, 0, 0, 0, 0, 15, 11, 101
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\gui.py", "Python", 283, 0, 0, 0, 0, 0, 0, 0, 23, 50, 356
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\introduce.md", "Markdown", 0, 215, 0, 0, 0, 0, 0, 0, 0, 45, 260
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\main.py", "Python", 156, 0, 0, 0, 0, 0, 0, 0, 27, 36, 219
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\modules\__init__.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 2
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\modules\blacklist_checker.py", "Python", 23, 0, 0, 0, 0, 0, 0, 0, 6, 5, 34
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\modules\browser_info_fetcher.py", "Python", 130, 0, 0, 0, 0, 0, 0, 0, 23, 30, 183
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\modules\managers.py", "Python", 279, 0, 0, 0, 0, 0, 0, 0, 68, 33, 380
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\modules\tab_closer.py", "Python", 179, 0, 0, 0, 0, 0, 0, 0, 39, 45, 263
"d:\MyProjects\Selfdiscipline\Combination\intelligent_monitor\test_modules.py", "Python", 118, 0, 0, 0, 0, 0, 0, 0, 18, 26, 162
"d:\MyProjects\Selfdiscipline\Combination\proxy\requesttest.py", "Python", 216, 0, 0, 0, 0, 0, 0, 0, 30, 45, 291
"d:\MyProjects\Selfdiscipline\Combination\proxy\user_agent_cache.json", "JSON", 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 3
"d:\MyProjects\Selfdiscipline\Combination\test_fusion.py", "Python", 165, 0, 0, 0, 0, 0, 0, 0, 34, 46, 245
"d:\MyProjects\Selfdiscipline\Combination\uninstall.py", "Python", 97, 0, 0, 0, 0, 0, 0, 0, 17, 20, 134
"d:\MyProjects\Selfdiscipline\Combination\融合蓝图.md", "Markdown", 0, 178, 0, 0, 0, 0, 0, 0, 0, 40, 218
"d:\MyProjects\Selfdiscipline\Combination\项目蓝图.md", "Markdown", 0, 196, 0, 0, 0, 0, 0, 0, 0, 83, 279
"Total", "-", 5508, 1181, 13, 60218, 3, 267, 429, 268, 1545, 3158, 72590