# Summary

Date : 2025-07-16 21:05:55

Directory d:\\MyProjects\\Selfdiscipline\\Combination

Total : 60 files,  67887 codes, 1545 comments, 3158 blanks, all 72590 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| XML | 16 | 60,218 | 0 | 1,377 | 61,595 |
| Python | 30 | 5,508 | 1,479 | 1,309 | 8,296 |
| Markdown | 8 | 1,181 | 6 | 334 | 1,521 |
| JavaScript | 1 | 429 | 31 | 58 | 518 |
| HTML | 1 | 268 | 11 | 12 | 291 |
| PostCSS | 1 | 267 | 18 | 64 | 349 |
| PowerShell | 2 | 13 | 0 | 4 | 17 |
| JSON | 1 | 3 | 0 | 0 | 3 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 60 | 67,887 | 1,545 | 3,158 | 72,590 |
| . (Files) | 7 | 1,201 | 90 | 330 | 1,621 |
| Interop.UIAutomationClient.10.19041.0 | 2 | 16 | 0 | 2 | 18 |
| Interop.UIAutomationClient.10.19041.0\\build | 1 | 13 | 0 | 1 | 14 |
| Interop.UIAutomationClient.10.19041.0\\tools | 1 | 3 | 0 | 1 | 4 |
| System.CodeDom.8.0.0 | 7 | 22,022 | 0 | 262 | 22,284 |
| System.CodeDom.8.0.0\\buildTransitive | 2 | 12 | 0 | 2 | 14 |
| System.CodeDom.8.0.0\\buildTransitive\\net461 | 1 | 6 | 0 | 1 | 7 |
| System.CodeDom.8.0.0\\buildTransitive\\netcoreapp2.0 | 1 | 6 | 0 | 1 | 7 |
| System.CodeDom.8.0.0\\lib | 5 | 22,010 | 0 | 260 | 22,270 |
| System.CodeDom.8.0.0\\lib\\net462 | 1 | 4,402 | 0 | 52 | 4,454 |
| System.CodeDom.8.0.0\\lib\\net6.0 | 1 | 4,402 | 0 | 52 | 4,454 |
| System.CodeDom.8.0.0\\lib\\net7.0 | 1 | 4,402 | 0 | 52 | 4,454 |
| System.CodeDom.8.0.0\\lib\\net8.0 | 1 | 4,402 | 0 | 52 | 4,454 |
| System.CodeDom.8.0.0\\lib\\netstandard2.0 | 1 | 4,402 | 0 | 52 | 4,454 |
| System.Management.8.0.0 | 9 | 38,224 | 6 | 1,138 | 39,368 |
| System.Management.8.0.0 (Files) | 1 | 41 | 6 | 24 | 71 |
| System.Management.8.0.0\\buildTransitive | 1 | 6 | 0 | 1 | 7 |
| System.Management.8.0.0\\buildTransitive\\netcoreapp2.0 | 1 | 6 | 0 | 1 | 7 |
| System.Management.8.0.0\\lib | 4 | 10,116 | 0 | 0 | 10,116 |
| System.Management.8.0.0\\lib\\net6.0 | 1 | 2,529 | 0 | 0 | 2,529 |
| System.Management.8.0.0\\lib\\net7.0 | 1 | 2,529 | 0 | 0 | 2,529 |
| System.Management.8.0.0\\lib\\net8.0 | 1 | 2,529 | 0 | 0 | 2,529 |
| System.Management.8.0.0\\lib\\netstandard2.0 | 1 | 2,529 | 0 | 0 | 2,529 |
| System.Management.8.0.0\\runtimes | 3 | 28,061 | 0 | 1,113 | 29,174 |
| System.Management.8.0.0\\runtimes\\win | 3 | 28,061 | 0 | 1,113 | 29,174 |
| System.Management.8.0.0\\runtimes\\win\\lib | 3 | 28,061 | 0 | 1,113 | 29,174 |
| System.Management.8.0.0\\runtimes\\win\\lib\\net6.0 | 1 | 9,401 | 0 | 371 | 9,772 |
| System.Management.8.0.0\\runtimes\\win\\lib\\net7.0 | 1 | 9,330 | 0 | 371 | 9,701 |
| System.Management.8.0.0\\runtimes\\win\\lib\\net8.0 | 1 | 9,330 | 0 | 371 | 9,701 |
| UIAComWrapper.1.1.0.14 | 1 | 10 | 0 | 3 | 13 |
| UIAComWrapper.1.1.0.14\\tools | 1 | 10 | 0 | 3 | 13 |
| backend | 2 | 929 | 235 | 261 | 1,425 |
| common | 9 | 1,569 | 700 | 410 | 2,679 |
| frontend | 5 | 1,666 | 153 | 262 | 2,081 |
| frontend (Files) | 2 | 702 | 93 | 128 | 923 |
| frontend\\static | 2 | 696 | 49 | 122 | 867 |
| frontend\\static\\css | 1 | 267 | 18 | 64 | 349 |
| frontend\\static\\js | 1 | 429 | 31 | 58 | 518 |
| frontend\\templates | 1 | 268 | 11 | 12 | 291 |
| installer | 1 | 310 | 76 | 93 | 479 |
| intelligent_monitor | 15 | 1,721 | 255 | 352 | 2,328 |
| intelligent_monitor (Files) | 8 | 1,035 | 102 | 226 | 1,363 |
| intelligent_monitor\\core | 2 | 75 | 16 | 12 | 103 |
| intelligent_monitor\\modules | 5 | 611 | 137 | 114 | 862 |
| proxy | 2 | 219 | 30 | 45 | 294 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)